#!/usr/bin/env python3
"""
单文件版：Congress.gov API v3 2025 对华相关法案“全文-only”抓取器
- 列表页(title+latestAction)关键词预筛选 -> 命中后抓取 /text 二跳下载（txt/html/pdf）
- pdf 使用 pdfminer.six 提取纯文本；html 去标签；统一保存为 UTF-8 文本
- 仅保存“全文”，无全文则跳过；生成 manifest.csv
- 输出目录默认：数据/2025_txt/
运行：设置环境变量 CONGRESS_API_KEY 后执行 python main.py
"""
import os
import re
import sys
import csv
import time
import asyncio
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import httpx
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from loguru import logger

# --------------------------- Config ---------------------------
class Config:
    API_BASE_URL: str = "https://api.congress.gov/v3"
    CONGRESS_API_KEY: str = os.getenv("CONGRESS_API_KEY", "")
    FROM_DATETIME: str = os.getenv("FROM_DATETIME", "2025-01-01T00:00:00Z")
    OUTPUT_DIR: Path = Path(os.getenv("OUTPUT_DIR", "数据/2025_txt/"))
    RATE_LIMIT: float = float(os.getenv("RATE_LIMIT", "2"))
    MAX_RETRIES: int = int(os.getenv("MAX_RETRIES", "5"))
    REQUEST_TIMEOUT: int = int(os.getenv("REQUEST_TIMEOUT", "30"))
    TARGET_HITS: int = int(os.getenv("TARGET_HITS", "200"))
    PAGE_LIMIT: int = int(os.getenv("PAGE_LIMIT", "250"))
    MAX_TEXT_LENGTH: int = int(os.getenv("MAX_TEXT_LENGTH", "50000"))

    @classmethod
    def validate(cls) -> None:
        if not cls.CONGRESS_API_KEY:
            raise ValueError("CONGRESS_API_KEY 未设置")
        cls.OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    @classmethod
    def headers(cls) -> Dict[str,str]:
        return {
            "X-Api-Key": cls.CONGRESS_API_KEY,
            "User-Agent": "Congress-China-Bills-Fetcher/1.0",
            "Accept": "application/json",
        }

# --------------------------- Rate limiter ---------------------------
class RateLimiter:
    def __init__(self, rate: float):
        self.rate = rate
        self.tokens = rate
        self.last = time.time()
    async def acquire(self):
        now = time.time()
        self.tokens = min(self.rate, self.tokens + (now - self.last) * self.rate)
        self.last = now
        if self.tokens >= 1:
            self.tokens -= 1
        else:
            await asyncio.sleep((1 - self.tokens) / self.rate)
            self.tokens = 0

# --------------------------- http client ---------------------------
class CongressAPIClient:
    def __init__(self):
        self.base = Config.API_BASE_URL
        self.headers = Config.headers()
        self.limiter = RateLimiter(Config.RATE_LIMIT)
        self.client = httpx.AsyncClient(timeout=Config.REQUEST_TIMEOUT, headers=self.headers,
                                        limits=httpx.Limits(max_keepalive_connections=10, max_connections=20))
    async def __aenter__(self):
        return self
    async def __aexit__(self, exc_type, exc, tb):
        await self.client.aclose()

    @retry(stop=stop_after_attempt(Config.MAX_RETRIES),
           wait=wait_exponential(multiplier=1, min=2, max=60),
           retry=retry_if_exception_type((httpx.HTTPStatusError, httpx.RequestError)))
    async def _get(self, url: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        await self.limiter.acquire()
        r = await self.client.get(url, params=params)
        if r.status_code == 429:
            ra = int(r.headers.get("Retry-After", 60))
            logger.warning(f"429 限流，等待 {ra}s")
            await asyncio.sleep(ra)
            r.raise_for_status()  # 触发重试
        r.raise_for_status()
        return r.json()

    async def list_bills(self, offset: int, limit: int) -> Dict[str, Any]:
        url = f"{self.base}/bill"
        params = {"fromDateTime": Config.FROM_DATETIME, "format": "json", "limit": limit, "offset": offset}
        logger.info(f"获取列表 offset={offset}, limit={limit}")
        return await self._get(url, params)

    async def get_text_meta(self, congress: int, bill_type: str, number: int) -> Optional[Dict[str, Any]]:
        url = f"{self.base}/bill/{congress}/{bill_type}/{number}/text"
        try:
            return await self._get(url, {"format": "json"})
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                return None
            raise

# --------------------------- keyword matcher ---------------------------
CHINA_KEYWORDS = [
    # 国家/地理
    "china","prc","people's republic of china","chinese","beijing","mainland","taiwan","hong kong","xinjiang","uyghur","tibet","tibetan","prc nationals",
    # 机构/机制
    "ccp","communist party of china","cfius","firrma","entity list","export control","export controls","sanction","sanctions","de-risking",
    # 产业/技术/供应链
    "semiconductor","semiconductors","chip","chips","ai","5g","rare earth","supply chain","critical mineral","critical minerals","telecom",
    # 企业/平台
    "huawei","zte","bytedance","tiktok","tencent","wechat","alibaba","ant group","dji","hikvision","dahua","smic","ymtc","shein","temu","byd",
    # 人权/执法/经贸
    "forced labor","uflpa","anti-dumping","export ban","investment screening","outbound investment",
]
BOUNDARY = {"china","chinese","ai","chip","chips"}

def normalize(s: str) -> str:
    s = s.lower()
    s = re.sub(r"[\t\r\n]", " ", s)
    s = re.sub(r"\s+", " ", s).strip()
    return s

def is_china_related(*texts: str) -> Tuple[bool, List[str]]:
    joined = " ".join(t for t in texts if t)
    if not joined:
        return False, []
    n = normalize(joined)
    hits = set()
    # 词边界优先
    for w in BOUNDARY:
        if re.search(rf"\b{re.escape(w)}\b", n, flags=re.IGNORECASE):
            hits.add(w)
    # 子串匹配
    for k in CHINA_KEYWORDS:
        if k in n:
            hits.add(k)
    return (len(hits) > 0), sorted(hits)[:10]

# --------------------------- IO helpers ---------------------------
@dataclass
class BillInfo:
    congress: int
    bill_type: str  # 小写：hr/s/hres/sres...
    number: int
    title: str
    latest_action_date: str
    latest_action_text: str
    url: str
    @property
    def bill_id(self) -> str:
        return f"{self.congress}-{self.bill_type}-{self.number}"

@dataclass
class BillContent:
    bill: BillInfo
    content: str
    source_type: str  # text
    matched_keywords: List[str]
    score: float

class FileManager:
    def __init__(self):
        self.out = Config.OUTPUT_DIR
        self.manifest = self.out / "manifest.csv"
        self.errors = self.out / "errors.log"
        self.out.mkdir(parents=True, exist_ok=True)
    def save_one(self, bc: BillContent, rank: int) -> str:
        # 文件名
        try:
            dt = datetime.fromisoformat(bc.bill.latest_action_date.replace('Z','+00:00')).strftime('%Y%m%d')
        except Exception:
            dt = datetime.utcnow().strftime('%Y%m%d')
        name = f"{rank:04d}_{bc.bill.congress}-{bc.bill.bill_type}-{bc.bill.number}_{dt}_text.txt"
        path = self.out / name
        header = [
            f"### title: {bc.bill.title}",
            f"### billId: {bc.bill.bill_type}.{bc.bill.number} ({bc.bill.congress}th)",
            f"### latestActionDate: {bc.bill.latest_action_date}",
            f"### url: {bc.bill.url}",
            f"### source: Congress.gov API v3 (full text)",
            "",
        ]
        txt = "\n".join(header) + bc.content
        path.write_text(txt, encoding='utf-8', newline='\n')
        return name
    def save_all(self, items: List[BillContent]) -> None:
        rows = []
        for i, bc in enumerate(items, 1):
            fname = self.save_one(bc, i)
            rows.append({
                "rank": i,
                "congress": bc.bill.congress,
                "billType": bc.bill.bill_type,
                "billNumber": bc.bill.number,
                "latestActionDate": bc.bill.latest_action_date,
                "sourceUsed": bc.source_type,
                "txtPath": fname,
                "url": bc.bill.url,
                "matchScore": f"{bc.score:.2f}",
                "matchedKeywords": "|".join(bc.matched_keywords),
            })
        if rows:
            with self.manifest.open('w', encoding='utf-8', newline='') as f:
                w = csv.DictWriter(f, fieldnames=list(rows[0].keys()))
                w.writeheader(); w.writerows(rows)
    def save_errors(self, errs: List[Dict[str,Any]]):
        if not errs: return
        with self.errors.open('w', encoding='utf-8') as f:
            for e in errs:
                f.write(str(e)+"\n")
    def stats(self) -> Dict[str, Any]:
        txts = list(self.out.glob("*.txt"))
        return {
            "output_dir": str(self.out),
            "txt_files_count": len(txts),
            "manifest_exists": self.manifest.exists(),
            "errors_log_exists": self.errors.exists(),
            "errors_count": sum(1 for _ in self.errors.open('r', encoding='utf-8')) if self.errors.exists() else 0,
            "sample_files": [t.name for t in txts[:3]],
        }

# --------------------------- Core processing ---------------------------
class Processor:
    def __init__(self):
        self.seen = set()
        self.hits: List[BillContent] = []
        self.errors: List[Dict[str,Any]] = []
    async def run(self):
        offset = 0
        async with CongressAPIClient() as api:
            while len(self.hits) < Config.TARGET_HITS:
                try:
                    resp = await api.list_bills(offset, Config.PAGE_LIMIT)
                    bills = resp.get("bills", [])
                    if not bills:
                        logger.info("无更多法案，结束")
                        break
                    for b in bills:
                        info = self._parse(b)
                        if info.key in self.seen:
                            continue
                        self.seen.add(info.key)
                        # 预筛选：只看标题+最新动作
                        pre_ok, _ = is_china_related(info.title, info.latest_action_text)
                        if not pre_ok:
                            continue
                        # 获取全文
                        content = await self._fetch_full_text(api, info)
                        if not content:
                            continue
                        # 二次判断（含全文）
                        ok, kws = is_china_related(info.title, info.latest_action_text, content)
                        if not ok:
                            continue
                        score = min(1.0, 0.5 + min(0.4, 0.1*len(kws)) + (0.1 if any(k in kws for k in ["china","chinese","prc","taiwan"]) else 0.0))
                        self.hits.append(BillContent(info, content, "text", kws, score))
                        logger.info(f"命中：{info.bill_id} (分数 {score:.2f})，当前 {len(self.hits)}/{Config.TARGET_HITS}")
                        if len(self.hits) >= Config.TARGET_HITS:
                            break
                    offset += Config.PAGE_LIMIT
                except Exception as e:
                    logger.error(f"列表处理异常: {e}")
                    self.errors.append({"type":"list","offset":offset,"error":str(e),"ts":datetime.utcnow().isoformat()})
                    break
        return self.hits

    def _parse(self, b: Dict[str,Any]) -> BillInfo:
        return BillInfo(
            congress=int(b.get("congress")),
            bill_type=str(b.get("type"," ")).lower(),
            number=int(b.get("number")),
            title=b.get("title",""),
            latest_action_date=b.get("latestAction",{}).get("actionDate",""),
            latest_action_text=b.get("latestAction",{}).get("text",""),
            url=b.get("url",""),
        )

    async def _fetch_full_text(self, api: CongressAPIClient, info: BillInfo) -> str:
        meta = await api.get_text_meta(info.congress, info.bill_type, info.number)
        if not meta:
            return ""
        data = meta.get("data") or meta
        versions = data.get("textVersions", [])
        if not versions:
            return ""
        latest = versions[0]
        formats = latest.get("formats", [])
        chosen = None
        for typ in ["txt","html","pdf"]:
            for f in formats:
                if f.get("type","" ).lower()==typ and f.get("url"):
                    chosen = (typ, f["url"]) ; break
            if chosen: break
        if not chosen:
            return ""
        typ, url = chosen
        try:
            async with httpx.AsyncClient(timeout=Config.REQUEST_TIMEOUT) as dl:
                r = await dl.get(url)
                if r.status_code != 200:
                    return ""
                if typ == "txt":
                    raw = r.text
                elif typ == "html":
                    raw = re.sub(r"<[^>]+>", "\n", r.text)
                else: # pdf
                    try:
                        from io import BytesIO
                        from pdfminer.high_level import extract_text
                        raw = extract_text(BytesIO(r.content)) or ""
                    except Exception as e:
                        logger.debug(f"PDF 提取失败 {info.bill_id}: {e}")
                        raw = ""
                raw = re.sub(r"\r\n?", "\n", raw)
                if len(raw) > Config.MAX_TEXT_LENGTH:
                    raw = raw[:Config.MAX_TEXT_LENGTH] + "\n[TRUNCATED]"
                return raw.strip()
        except Exception as e:
            logger.debug(f"全文下载失败 {info.bill_id}: {e}")
            return ""

# --------------------------- logging & UI ---------------------------
def setup_logging():
    logger.remove()
    logger.add(sys.stderr,
               format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
               level="INFO")
    logger.add(Config.OUTPUT_DIR / "congress_fetcher.log",
               format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {function}:{line} - {message}",
               level="DEBUG", rotation="10 MB", retention="7 days")

def print_banner():
    print("""
╔══════════════════════════════════════════════════════════════════════╗
║            Congress.gov API v3 对华相关法案“全文-only”抓取工具               ║
╚══════════════════════════════════════════════════════════════════════╝
""")

def print_config():
    logger.info("=== 配置 ===")
    logger.info(f"API: {Config.API_BASE_URL}")
    logger.info(f"fromDateTime: {Config.FROM_DATETIME}")
    logger.info(f"输出目录: {Config.OUTPUT_DIR}")
    logger.info(f"目标命中数: {Config.TARGET_HITS}")
    logger.info(f"限速: {Config.RATE_LIMIT} req/s, 每页: {Config.PAGE_LIMIT}")
    logger.info(f"超时/重试: {Config.REQUEST_TIMEOUT}s / {Config.MAX_RETRIES}")

async def main():
    setup_logging(); print_banner()
    Config.validate(); print_config()
    proc = Processor(); fm = FileManager()
    hits = await proc.run()
    if not hits:
        logger.warning("未找到符合条件的法案")
        return
    # 排序保存
    hits.sort(key=lambda x: x.score, reverse=True)
    fm.save_all(hits)
    if proc.errors:
        fm.save_errors(proc.errors)
    # 统计
    st = fm.stats()
    logger.info(f"保存文件数量: {st['txt_files_count']} 输出目录: {st['output_dir']}")
    logger.info(f"manifest.csv: {'✅' if st['manifest_exists'] else '❌'}  errors.log: {'✅' if st['errors_log_exists'] else '❌'} ({st['errors_count']} 条)")

if __name__ == "__main__":
    if sys.version_info < (3,8):
        print("需要 Python 3.8+"); sys.exit(1)
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n中断退出")
        sys.exit(1)

