# -*- coding: utf-8 -*-
"""
事件抽取 V1：
- BillEvent：从 Congress.gov 官方 API 的 actions 映射
- PolicyMeasure：基于句级规则 + 词典 + NER 对齐

运行示例：
python processing/events/extract_events_v1.py --outdir artifacts/events_v1 \
  --txtdir out/2025_txt --manifest out/2025_txt/manifest.csv \
  --mentions artifacts/ner_v1/entities_mentions.jsonl --canon artifacts/ner_v1/entities_canonical.jsonl
"""
from __future__ import annotations
import os, sys, csv, json, time, re
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, Any, List, Tuple, Iterable, Optional, Set
from datetime import datetime
import requests

API_BASES = [
    "https://api.data.gov/congress/v3",
    "https://api.congress.gov/v3",
]
RATE_LIMIT_PER_SEC = 3.0

# 输出目录结构
OUTDIR = Path("artifacts/events_v1")
OUT_BILL_EVENTS = OUTDIR / "bill_events.csv"
OUT_POLICY = OUTDIR / "policy_measures.csv"
LOG_PATH = OUTDIR / "run.log"

# --------------------------- 工具 ---------------------------

def jloads(line: str) -> Dict[str, Any]:
    try:
        import orjson
        return orjson.loads(line)
    except Exception:
        return json.loads(line)


def slugify(s: str) -> str:
    return re.sub(r"[^a-z0-9]+", "-", s.lower()).strip("-")


def now_utc() -> str:
    return datetime.utcnow().isoformat() + "Z"


# --------------------------- 输入解析 ---------------------------
@dataclass
class BillMeta:
    bill_id: str  # e.g., 119-hr-4830
    bill_key: str  # e.g., 119|hr|4830
    title: str
    latestActionDate: str
    url: str
    txt_path: Path


def parse_header(text: str) -> Tuple[Dict[str,str], str]:
    lines = text.splitlines()
    meta = {"title":"", "billId":"", "latestActionDate":"", "url":"", "source":""}
    header_len = 0
    for i in range(min(5, len(lines))):
        line = lines[i].strip()
        if line.startswith("### "):
            header_len = i + 1
            k, v = line[4:].split(":", 1)
            meta[k.strip()] = v.strip()
    body = "\n".join(lines[header_len:]) if header_len > 0 else text
    # 解析 bill_key
    bill_key = ""
    m = re.search(r"([hs]\.?\s?(?:r|res|j\.?res|con\.?res)?)[\s\.]*(\d{1,5}).*?\((\d{2,3})th\)", meta.get("billId",""), flags=re.I)
    if m:
        bill_type = m.group(1).lower().replace(".", "").replace(" ", "")
        number = m.group(2)
        congress = m.group(3)
        bill_key = f"{congress}|{bill_type}|{number}"
    meta["bill_key"] = bill_key
    return meta, body


def load_manifest_or_filenames(txtdir: Path) -> List[BillMeta]:
    metas: List[BillMeta] = []
    man = txtdir / "manifest.csv"
    for p in sorted(txtdir.glob("*.txt")):
        txt = p.read_text(encoding="utf-8", errors="ignore")
        meta, _ = parse_header(txt)
        bill_key = meta.get("bill_key", "")
        if not bill_key:
            # 从文件名解析 0001_119-hr-4830_20250801_text.txt
            m = re.search(r"\d{4}_(\d+)-([a-z]+)-(\d+)_", p.name, flags=re.I)
            if m:
                bill_key = f"{m.group(1)}|{m.group(2)}|{m.group(3)}"
        if not bill_key:
            continue
        congress, bill_type, number = bill_key.split("|")
        bill_id = f"{congress}-{bill_type}-{number}"
        metas.append(BillMeta(
            bill_id=bill_id,
            bill_key=bill_key,
            title=meta.get("title", ""),
            latestActionDate=meta.get("latestActionDate", ""),
            url=meta.get("url", ""),
            txt_path=p,
        ))
    return metas


# --------------------------- NER 对齐索引 ---------------------------
@dataclass
class Mention:
    doc_id: str
    bill_key: str
    start: int
    end: int
    sentence: str
    label: str
    mention: str
    canonical_id: str


def build_mentions_index(mentions_path: Path) -> Dict[str, List[Mention]]:
    idx: Dict[str, List[Mention]] = {}
    with mentions_path.open("r", encoding="utf-8") as f:
        for line in f:
            if not line.strip():
                continue
            rec = jloads(line)
            cid = rec.get("meta", {}).get("canonical_id") or None
            # 若 mentions.jsonl 未包含 canonical_id，则回退使用推导（与 summarize 脚本保持一致）
            if not cid:
                lab = rec.get("label", "")
                text = rec.get("mention", "").strip().lower()
                cid = f"{lab.lower()}:{slugify(text)}" if lab and text else ""
            m = Mention(
                doc_id=rec.get("doc_id",""),
                bill_key=rec.get("bill_key",""),
                start=int(rec.get("start",0)),
                end=int(rec.get("end",0)),
                sentence=rec.get("sentence",""),
                label=rec.get("label",""),
                mention=rec.get("mention",""),
                canonical_id=cid,
            )
            idx.setdefault(m.bill_key, []).append(m)
    return idx


# --------------------------- Congress API 客户端 ---------------------------
class CongressClient:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.sess = requests.Session()
        # 兼容两种 Header 名
        self.sess.headers.update({"X-Api-Key": api_key, "X-API-Key": api_key, "Accept": "application/json", "User-Agent": "events-v1/0.1"})
        self.last_req_ts = 0.0
        self.base_index = 0

    def _throttle(self):
        now = time.time()
        delta = now - self.last_req_ts
        min_interval = 1.0 / RATE_LIMIT_PER_SEC
        if delta < min_interval:
            time.sleep(min_interval - delta)
        self.last_req_ts = time.time()

    def _get(self, path: str, params: Dict[str,Any]) -> Optional[Dict[str,Any]]:
        retries = 5
        backoff = 1.0
        for i in range(retries):
            try:
                self._throttle()
                base = API_BASES[self.base_index % len(API_BASES)]
                url = f"{base}{path}"
                r = self.sess.get(url, params=params, timeout=30)
                if r.status_code == 429 or r.status_code >= 500:
                    time.sleep(backoff)
                    backoff *= 2
                    continue
                if r.status_code == 404 and self.base_index < len(API_BASES)-1:
                    # 切换备用域名重试
                    self.base_index += 1
                    continue
                if r.status_code != 200:
                    return None
                return r.json()
            except Exception:
                time.sleep(backoff)
                backoff *= 2
        return None

    def get_actions(self, congress: str, bill_type: str, number: str) -> List[Dict[str, Any]]:
        # 优先使用 api.congress.gov（data.gov 路由常报 403）
        self.base_index = 1  # 1 -> api.congress.gov
        data = self._get(f"/bill/{congress}/{bill_type}/{number}/actions", {"format": "json"})
        if not data:
            # 回退到 data.gov
            self.base_index = 0
            data = self._get(f"/bill/{congress}/{bill_type}/{number}/actions", {"format": "json"})
        if not data:
            return []
        return data.get("actions", [])


# --------------------------- 立法事件映射 ---------------------------
ACTION_RULES = [
    (re.compile(r"\bintroduced\b", re.I), "INTRODUCED"),
    (re.compile(r"\b(referred to|referral)\b", re.I), "REFERRED"),
    (re.compile(r"\breported( by)?\b", re.I), "REPORTED"),
    (re.compile(r"\bpassed .* house\b", re.I), "PASSED_HOUSE"),
    (re.compile(r"\bpassed .* senate\b", re.I), "PASSED_SENATE"),
    (re.compile(r"\b(conference|conferees)\b", re.I), "CONFERENCE"),
    (re.compile(r"\bpresented to the president\b", re.I), "PRESENTED_TO_PRESIDENT"),
    (re.compile(r"\benrolled( bill)?\b", re.I), "ENROLLED"),
    (re.compile(r"\bbecame public law\b", re.I), "BECAME_PUBLIC_LAW"),
    (re.compile(r"\bvetoed\b", re.I), "VETOED"),
    (re.compile(r"\bcloture\b", re.I), "CLOTURE_MOTION"),
    (re.compile(r"\bamend(ed)?\b", re.I), "AMENDED"),
]


def map_action_to_type(text: str) -> Optional[str]:
    t = text.lower()
    for pat, typ in ACTION_RULES:
        if pat.search(t):
            return typ
    return None


# --------------------------- 政策措施规则 ---------------------------
POLICY_TRIGGERS = {
    "PROCUREMENT_RESTRICTION": [r"prohibit(ed|s)? procurement|no funds may be used to procure|ban procurement|restrict procurement"],
    "EXPORT_CONTROL": [r"export control|\bEAR\b|entity list|BIS|license requirement|foreign direct product rule"],
    "INVESTMENT_SCREENING": [r"\bCFIUS\b|FIRRMA|outbound investment|screen|review|notify"],
    "SANCTION": [r"sanction|designate|blocking property|OFAC|SDN list"],
    "APP_DATA_SECURITY": [r"data security|personal data|TikTok|ByteDance|WeChat"],
    "SUPPLY_CHAIN_REQUIREMENT": [r"forced labor|UFLPA|origin|supply chain|de minimis"],
    "FUNDING_PROHIBITION": [r"appropriation.*prohibit|no funds may be used"],
    "REPORTING_REQUIREMENT": [r"report to congress|reporting requirement|submit (an )?annual report"],
}
POLICY_PATTERNS = {k: [re.compile(p, re.I) for p in v] for k, v in POLICY_TRIGGERS.items()}


def detect_policy_type(sentence: str) -> Optional[Tuple[str, str]]:
    for typ, pats in POLICY_PATTERNS.items():
        for pat in pats:
            if pat.search(sentence):
                return typ, pat.pattern
    return None


# --------------------------- 事件生成 ---------------------------

def extract_bill_events(client: CongressClient, bills: List[BillMeta]) -> List[Dict[str, Any]]:
    events: List[Dict[str, Any]] = []
    for bm in bills:
        congress, bill_type, number = bm.bill_key.split("|")
        actions = client.get_actions(congress, bill_type, number)
        seq = 0
        for a in actions:
            txt = a.get("text") or a.get("action") or ""
            date = a.get("actionDate") or a.get("date") or ""
            chamber = a.get("chamber") or ""
            committee = a.get("committee") or a.get("committees") or ""
            actor = ""
            t = map_action_to_type(txt)
            if not t:
                continue
            seq += 1
            event = {
                "event_id": f"be:{bm.bill_id}:{date.replace('-','') or seq:>04}",
                "type": t,
                "date": date,
                "bill_key": bm.bill_key,
                "chamber": chamber,
                "committee": committee,
                "action_text": txt,
                "actor": actor,
            }
            events.append(event)
    return events


def extract_policy_measures(bills: List[BillMeta], mentions_idx: Dict[str, List[Mention]]) -> List[Dict[str, Any]]:
    results: List[Dict[str, Any]] = []
    for bm in bills:
        # 读取正文（用于句级匹配）
        text = bm.txt_path.read_text(encoding="utf-8", errors="ignore")
        _, body = parse_header(text)
        # 简易句分割（对 body 分割，保证与 mentions 偏移一致）
        sentences = re.split(r"(?<=[\.!?])\s+", body)
        # 建立 mentions 对齐（同一 bill_key 下的 mentions）
        bill_mentions = mentions_idx.get(bm.bill_key, [])
        tmp: List[Dict[str, Any]] = []
        for i, sent in enumerate(sentences):
            det = detect_policy_type(sent)
            if not det:
                continue
            typ, trigger = det
            # 找该句内的 mentions → canonical_id（在 body 中定位）
            s_start = body.find(sent)
            s_end = s_start + len(sent) if s_start >= 0 else -1
            targets: Set[str] = set()
            if s_start >= 0:
                for m in bill_mentions:
                    if m.start >= s_start and m.end <= s_end:
                        # 仅保留允许的目标类型
                        if m.label in {"CUSTOM_COMPANY","CUSTOM_AGENCY","CUSTOM_PLACE_CN","ORG","GPE","CUSTOM_POLICY","CUSTOM_TECH"}:
                            targets.add(m.canonical_id)
            # 简单 scope 提取（示例）：常见限定短语
            scope = ""
            for kw in ["federal agencies", "contractors", "covered application", "executive agencies", "department of", "secretary of"]:
                if kw in sent.lower():
                    scope = kw; break
            ev = {
                "event_id": f"pm:{bm.bill_id}:{i}",
                "type": typ,
                "date": "",
                "bill_key": bm.bill_key,
                "sentence": sent[:800],
                "trigger": trigger,
                "target_ids": ";".join(sorted(targets)) if targets else "",
                "scope": scope,
                "details": sent[:800],
            }
            tmp.append(ev)
        # 相邻合并：同 type 且 target_ids 有交集
        merged: List[Dict[str, Any]] = []
        def ids_set(s: str) -> Set[str]:
            return set([x for x in s.split(";") if x]) if s else set()
        for ev in tmp:
            if merged and merged[-1]["type"] == ev["type"]:
                prev_ids = ids_set(merged[-1].get("target_ids",""))
                curr_ids = ids_set(ev.get("target_ids",""))
                if prev_ids and curr_ids and (prev_ids & curr_ids):
                    # 合并：details 拼接、targets 并集
                    new_ids = sorted(prev_ids | curr_ids)
                    merged[-1]["details"] = (merged[-1]["details"] + " " + ev["details"])[:800]
                    merged[-1]["sentence"] = merged[-1]["details"]
                    merged[-1]["target_ids"] = ";".join(new_ids)
                    continue
            merged.append(ev)
        # 限流：每 (bill,type) 仅保留前 15 条（按 targets 数量降序）
        by_type: Dict[str, List[Dict[str, Any]]] = {}
        for ev in merged:
            by_type.setdefault(ev["type"], []).append(ev)
        for typ, lst in by_type.items():
            lst.sort(key=lambda e: len(ids_set(e.get("target_ids",""))), reverse=True)
            results.extend(lst[:15])
    return results


# --------------------------- 主流程 ---------------------------

def main():
    import argparse
    ap = argparse.ArgumentParser()
    ap.add_argument("--outdir", type=str, default=str(OUTDIR))
    ap.add_argument("--txtdir", type=str, default="out/2025_txt")
    ap.add_argument("--manifest", type=str, default="out/2025_txt/manifest.csv")
    ap.add_argument("--mentions", type=str, default="artifacts/ner_v1/entities_mentions.jsonl")
    ap.add_argument("--canon", type=str, default="artifacts/ner_v1/entities_canonical.jsonl")
    args = ap.parse_args()

    outdir = Path(args.outdir); outdir.mkdir(parents=True, exist_ok=True)

    # 读取清单/文本
    txtdir = Path(args.txtdir)
    bills = load_manifest_or_filenames(txtdir)
    if not bills:
        print("未找到法案文本，退出")
        sys.exit(1)

    # 构建 mentions 索引
    mentions_idx = build_mentions_index(Path(args.mentions))

    # 初始化 API 客户端
    api_key = os.getenv("CONGRESS_API_KEY", "")
    if not api_key:
        print("警告：未设置 CONGRESS_API_KEY，将无法调用 actions API；BillEvent 将为空")
    client = CongressClient(api_key) if api_key else None

    # 抽取 BillEvent
    bill_events: List[Dict[str, Any]] = []
    if client:
        bill_events = extract_bill_events(client, bills)

    # 抽取 PolicyMeasure
    policy_measures = extract_policy_measures(bills, mentions_idx)

    # 写出 CSV
    with (outdir / "bill_events.csv").open("w", encoding="utf-8", newline="") as f:
        w = csv.writer(f); w.writerow(["event_id","type","date","bill_key","chamber","committee","action_text","actor"])
        for e in bill_events:
            w.writerow([e.get("event_id"),e.get("type"),e.get("date"),e.get("bill_key"),e.get("chamber"),e.get("committee"),e.get("action_text"),e.get("actor")])

    with (outdir / "policy_measures.csv").open("w", encoding="utf-8", newline="") as f:
        w = csv.writer(f); w.writerow(["event_id","type","date","bill_key","sentence","trigger","target_ids","scope","details"])
        for e in policy_measures:
            w.writerow([e.get("event_id"),e.get("type"),e.get("date"),e.get("bill_key"),e.get("sentence"),e.get("trigger"),e.get("target_ids"),e.get("scope"),e.get("details")])

    # 运行日志
    with (outdir / "run.log").open("w", encoding="utf-8") as f:
        f.write(f"{now_utc()} bills={len(bills)} bill_events={len(bill_events)} policy_measures={len(policy_measures)}\n")

    print(f"已输出: {outdir}")


if __name__ == "__main__":
    main()

