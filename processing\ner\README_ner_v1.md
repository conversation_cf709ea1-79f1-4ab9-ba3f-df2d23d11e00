# NER V1（对 2025 文本做命名实体与标准化）

本目录包含运行 NER V1 的脚本与词典，面向 数据爬取/数据/2025_txt 下的 200 篇英文文本，识别“对华政策”相关实体并标准化输出。

## 目录结构
- processing/ner/run_ner_v1.py
- processing/ner/lexicons/*.csv（companies/agencies/committees/policies/places_cn）
- artifacts/ner_v1/（脚本运行后自动生成）

## 依赖
- Python 3.10+
- spaCy >= 3.7
- 可选：spacy-transformers、torch（若使用 en_core_web_trf）
- pandas、regex、tqdm、orjson（可选）

安装（示例）：
```bash
pip install -U spacy pandas tqdm regex orjson
# 如需 Transformer 管线
pip install spacy-transformers torch --extra-index-url https://download.pytorch.org/whl/cpu
# 下载至少一个英文模型
python -m spacy download en_core_web_sm
# 如资源允许
python -m spacy download en_core_web_trf
```

## 运行
```bash
python processing/ner/run_ner_v1.py
```
脚本会读取 数据爬取/数据/2025_txt/*.txt，输出到 artifacts/ner_v1/：
- entities_mentions.jsonl
- entities_canonical.jsonl
- doc_entities_index.csv
- stats/entities_summary.csv
- lexicons/*（词典快照）
- logs/errors_ner.log
- models/spacy_docbin.spacy（预留）

## 实体与规则
- 通用：PERSON, ORG, GPE, LOC, NORP, LAW, DATE, MONEY
- 领域：CUSTOM_BILL, CUSTOM_COMMITTEE, CUSTOM_AGENCY, CUSTOM_COMPANY, CUSTOM_PLACE_CN, CUSTOM_POLICY, CUSTOM_TECH（预留）
- 词典：lexicons/*.csv（大小写不敏感、支持别名）
- 正则：法案编号/公共法律号（见代码中的 BILL_RE、PUBLIC_LAW_RE）

## 自检清单
- artifacts/ner_v1/entities_mentions.jsonl 行数 ≥ 2,000
- stats/entities_summary.csv 中的关键标签均非零（COMPANY/AGENCY/PLACE_CN/POLICY/BILL）
- 随机抽查 10 篇文档，前 20 提及准确率 ≥ 80%
- logs/errors_ner.log 应为空或仅少量不可解析文件

## 常见问题
- 显存/内存不足：自动回退 en_core_web_sm。
- 模型未安装：按上方命令下载模型。
- 词典更新：直接编辑 processing/ner/lexicons 下 CSV，重跑脚本。

