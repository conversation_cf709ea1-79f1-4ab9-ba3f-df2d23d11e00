# -*- coding: utf-8 -*-
"""
从事件与 NER 产物构建图谱 nodes/edges CSV（V1）

输入：
- artifacts/events_v1/bill_events.csv
- artifacts/events_v1/policy_measures.csv
- 数据爬取/数据/2025_txt/manifest.csv（或从文件名解析）
- artifacts/ner_v1/doc_entities_index.csv（用于 ABOUT 阈值）

输出（graph/exports）：
- nodes_*.csv（Bill/Org/Place/Topic/Event/Committee）
- edges_*.csv（HAS_EVENT/TARGETS/ABOUT/REFERRED_TO）
"""
from __future__ import annotations
import csv, re, json
from pathlib import Path
from typing import Dict, Any, List, Tuple, Set

EVENTS_DIR = Path("artifacts/events_v1")
GRAPH_DIR = Path("graph/exports")
GRAPH_DIR.mkdir(parents=True, exist_ok=True)
NER_DIR = Path("artifacts/ner_v1")


def slugify(s: str) -> str:
    return re.sub(r"[^a-z0-9]+", "-", s.lower()).strip("-")


def parse_committees_field(val: str) -> List[Tuple[str, str]]:
    """从 bill_events.csv 的 committee 字段解析 (name, systemCode) 列表"""
    if not val:
        return []
    names = re.findall(r"'name':\s*'([^']+)'", val)
    codes = re.findall(r"'systemCode':\s*'([^']+)'", val)
    out: List[Tuple[str, str]] = []
    if names:
        for i, n in enumerate(names):
            code = codes[i] if i < len(codes) else ""
            out.append((n, code))
        return out
    # 尝试 JSON 风格
    names = re.findall(r'"name":\s*"([^"]+)"', val)
    codes = re.findall(r'"systemCode":\s*"([^"]+)"', val)
    if names:
        for i, n in enumerate(names):
            code = codes[i] if i < len(codes) else ""
            out.append((n, code))
        return out
    # 兜底：作为一个名称
    return [(val.strip(), "")]


def committee_id(name: str, code: str) -> str:
    return f"committee:{code}" if code else f"committee:{slugify(name)}"


def load_doc_index(path: Path) -> Dict[str, List[Tuple[str, str, int]]]:
    """读取 doc_entities_index.csv -> bill_key -> [(canonical_id,label,count)]"""
    idx: Dict[str, List[Tuple[str, str, int]]] = {}
    if not path.exists():
        return idx
    with path.open("r", encoding="utf-8") as f:
        r = csv.DictReader(f)
        for row in r:
            bk = row.get("bill_key", "")
            cid = row.get("canonical_id", "")
            lab = row.get("label", "")
            try:
                cnt = int(row.get("count", "0"))
            except Exception:
                cnt = 0
            if bk and cid:
                idx.setdefault(bk, []).append((cid, lab, cnt))
    return idx


# --------------------------- 工具 ---------------------------

def read_csv(path: Path) -> List[Dict[str, str]]:
    rows: List[Dict[str, str]] = []
    with path.open("r", encoding="utf-8") as f:
        r = csv.DictReader(f)
        for row in r:
            rows.append(row)
    return rows


def write_csv(path: Path, header: List[str], rows: List[List[str]]):
    with path.open("w", encoding="utf-8", newline="") as f:
        w = csv.writer(f)
        w.writerow(header)
        for row in rows:
            w.writerow(row)


# --------------------------- 构建节点 ---------------------------

def parse_committees_field(val: str) -> List[str]:
    if not val:
        return []
    names: List[str] = []
    # 尝试用正则提取 'name': '...'
    for m in re.finditer(r"'name':\s*'([^']+)'", val):
        names.append(m.group(1))
    if names:
        return names
    # 退化处理：直接返回原字符串
    return [val]


def build_nodes(bill_events: List[Dict[str,str]], policy_measures: List[Dict[str,str]], manifest: Path, doc_index_path: Path) -> Dict[str, List[List[str]]]:
    # Bill 节点
    bills: Dict[str, Dict[str, str]] = {}
    if manifest.exists():
        with manifest.open("r", encoding="utf-8") as f:
            r = csv.DictReader(f)
            for row in r:
                bill_id = f"{row['congress']}-{row['billType']}-{row['billNumber']}"
                bills[bill_id] = {
                    ":ID(Bill)": bill_id,
                    "congress": row.get("congress",""),
                    "billType": row.get("billType",""),
                    "billNumber": row.get("billNumber",""),
                    "title": "",
                    "latestActionDate": row.get("latestActionDate",""),
                    "url": row.get("url",""),
                }
    # Event 节点
    events: Dict[str, Dict[str,str]] = {}
    for e in bill_events:
        eid = e["event_id"]
        events[eid] = {":ID(Event)": eid, "eventType": e["type"], "date": e.get("date", ""), "textSnippet": (e.get("action_text") or "")[:200]}
    for e in policy_measures:
        eid = e["event_id"]
        events[eid] = {":ID(Event)": eid, "eventType": e["type"], "date": e.get("date", ""), "textSnippet": (e.get("sentence") or "")[:200]}

    # Org/Place/Topic/Committee 节点
    orgs: Set[str] = set(); places: Set[str] = set(); topics: Set[str] = set(); committees: Set[str] = set()
    for e in policy_measures:
        tids = (e.get("target_ids") or "").split(";") if e.get("target_ids") else []
        for cid in tids:
            if not cid:
                continue
            if cid.startswith("custom_place_cn:") or cid.startswith("place:") or ":hong" in cid or ":taiwan" in cid or ":china" in cid:
                places.add(cid)
            elif cid.startswith("org:") or cid.startswith("company:") or cid.startswith("agency:") or cid.startswith("committee:"):
                orgs.add(cid)
        # Topic 来源：policy type
        topics.add(f"topic:{e['type'].lower()}")
    for e in bill_events:
        comm = (e.get("committee") or "").strip()
        if comm:
            committees.add(comm)

    nodes = {
        "nodes_bill.csv": [[b[":ID(Bill)"], b["congress"], b["billType"], b["billNumber"], b["title"], b["latestActionDate"], b["url"]] for b in bills.values()],
        "nodes_event.csv": [[v[":ID(Event)"], v["eventType"], v.get("date",""), v.get("textSnippet","" )] for v in events.values()],
        "nodes_org.csv": [[oid, oid.split(":",1)[1], ""] for oid in sorted(orgs)],
        "nodes_place.csv": [[pid, pid.split(":",1)[1], "place"] for pid in sorted(places)],
        "nodes_topic.csv": [[tid, tid.split(":",1)[1]] for tid in sorted(topics)],
        "nodes_committee.csv": [[committee_id(n, c), n, ""] for n, c in sorted({(n,c) for (n,c) in sum([parse_committees_field(e.get("committee","")) for e in bill_events], [])})],
    }
    return nodes


# --------------------------- 构建边 ---------------------------

def build_edges(bill_events: List[Dict[str,str]], policy_measures: List[Dict[str,str]]) -> Dict[str, List[List[str]]]:
    edges_has_event: List[List[str]] = []
    edges_targets: List[List[str]] = []
    edges_about: List[List[str]] = []
    edges_referred_to: List[List[str]] = []

    # Bill -> Event（HAS_EVENT）
    for e in bill_events + policy_measures:
        bill_key = e.get("bill_key","")
        if not bill_key:
            continue
        congress, bill_type, number = bill_key.split("|")
        bill_id = f"{congress}-{bill_type}-{number}"
        edges_has_event.append([bill_id, e["event_id"], "HAS_EVENT"])

    # REFERRED_TO：从 BillEvent 的 REFERRED 事件生成
    for e in bill_events:
        if e.get("type") == "REFERRED":
            comm = (e.get("committee") or "").strip()
            if comm:
                cid = f"committee:{slugify(comm)}"
                congress, bill_type, number = e.get("bill_key").split("|")
                bill_id = f"{congress}-{bill_type}-{number}"
                edges_referred_to.append([bill_id, cid, "REFERRED_TO"])

    # Event -> Targets（TARGETS，仅政策措施）
    for e in policy_measures:
        eid = e["event_id"]
        tids = (e.get("target_ids") or "").split(";") if e.get("target_ids") else []
        for cid in tids:
            if not cid:
                continue
            end_id = cid
            edges_targets.append([eid, end_id, "TARGETS"])

    # Bill -> Topic（ABOUT）：基于政策类型（保留，同时将基于实体计数的 ABOUT 在下游补充）
    for e in policy_measures:
        bill_key = e.get("bill_key","")
        if not bill_key:
            continue
        congress, bill_type, number = bill_key.split("|")
        bill_id = f"{congress}-{bill_type}-{number}"
        topic_id = f"topic:{e['type'].lower()}"
        edges_about.append([bill_id, topic_id, "ABOUT"])

    return {
        "edges_bill_has_event.csv": edges_has_event,
        "edges_event_targets.csv": edges_targets,
        "edges_bill_about.csv": edges_about,
        "edges_bill_referred_to.csv": edges_referred_to,
    }


# --------------------------- 主流程 ---------------------------

def main():
    bill_events = read_csv(EVENTS_DIR / "bill_events.csv")
    policy_measures = read_csv(EVENTS_DIR / "policy_measures.csv")
    nodes = build_nodes(bill_events, policy_measures, Path("数据爬取/数据/2025_txt/manifest.csv"))
    for name, rows in nodes.items():
        if name == "nodes_bill.csv":
            write_csv(GRAPH_DIR / name, [":ID(Bill)", "congress","billType","billNumber","title","latestActionDate","url"], rows)
        elif name == "nodes_event.csv":
            write_csv(GRAPH_DIR / name, [":ID(Event)", "eventType","date","textSnippet"], rows)
        elif name == "nodes_org.csv":
            write_csv(GRAPH_DIR / name, [":ID(Org)", "name","country"], rows)
        elif name == "nodes_place.csv":
            write_csv(GRAPH_DIR / name, [":ID(Place)", "name","type"], rows)
        elif name == "nodes_topic.csv":
            write_csv(GRAPH_DIR / name, [":ID(Topic)", "name"], rows)
        elif name == "nodes_committee.csv":
            write_csv(GRAPH_DIR / name, [":ID(Committee)", "name","chamber"], rows)
    edges = build_edges(bill_events, policy_measures)
    for name, rows in edges.items():
        if name == "edges_bill_has_event.csv":
            write_csv(GRAPH_DIR / name, [":START_ID(Bill)", ":END_ID(Event)", "role"], rows)
        elif name == "edges_event_targets.csv":
            write_csv(GRAPH_DIR / name, [":START_ID(Event)", ":END_ID", "role"], rows)
        elif name == "edges_bill_about.csv":
            write_csv(GRAPH_DIR / name, [":START_ID(Bill)", ":END_ID(Topic)", "role"], rows)
        elif name == "edges_bill_referred_to.csv":
            write_csv(GRAPH_DIR / name, [":START_ID(Bill)", ":END_ID(Committee)", "role"], rows)
    print(f"图谱 CSV 已输出到: {GRAPH_DIR}")


if __name__ == "__main__":
    main()

