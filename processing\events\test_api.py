# -*- coding: utf-8 -*-
import os, requests, sys
API_BASES = [
    "https://api.data.gov/congress/v3",
    "https://api.congress.gov/v3",
]
key = os.getenv("CONGRESS_API_KEY", "")
if not key:
    print("NO_KEY")
    sys.exit(1)
headers = {"X-Api-Key": key, "Accept": "application/json", "User-Agent": "events-v1/0.1"}
examples = [
    ("119", "hr", "4830"),
    ("119", "s", "2657"),
    ("119", "s", "2560"),
]
for base in API_BASES:
    for cong, bt, no in examples:
        url = f"{base}/bill/{cong}/{bt}/{no}/actions"
        try:
            r = requests.get(url, params={"format":"json"}, headers=headers, timeout=30)
            print("URL:", url, "status=", r.status_code)
            txt = r.text
            print("LEN=", len(txt))
            if r.status_code == 200:
                # 仅打印前 300 字符
                print(txt[:300].replace("\n"," ")[:300])
        except Exception as e:
            print("ERR:", e)

