# -*- coding: utf-8 -*-
"""
/********************************************************************************
 * NER V1: 对 2025 文本做英文命名实体识别与标准化
 *
 * 输入：数据爬取/数据/2025_txt/*.txt（文件头部 5 行元信息）
 * 输出：artifacts/ner_v1/*（mentions、canonical、index、stats、logs、docbin）
 *
 * 依赖：spacy>=3.7, spacy-transformers(可选), torch(可选), regex, pandas, orjson(可选), tqdm
 * 模型：优先 en_core_web_trf，资源不足时回退 en_core_web_sm
 ********************************************************************************/
"""
from __future__ import annotations
import os, re, sys, json, csv, shutil
from pathlib import Path
from typing import Dict, List, Tuple, Any, Iterable, Optional

# 第三方（允许缺省降级）
try:
    import orjson  # 更快的 JSON
except Exception:
    orjson = None  # type: ignore
try:
    import pandas as pd
except Exception:
    pd = None  # type: ignore

import spacy
from spacy.language import Language
from spacy.tokens import Doc, Span
from spacy.pipeline import EntityRuler
from spacy.util import filter_spans
from tqdm import tqdm

# --------------------------- 常量与路径 ---------------------------
ROOT = Path(".").resolve()
TXT_DIR = ROOT / "数据爬取" / "数据" / "2025_txt"
OUT_DIR = ROOT / "artifacts" / "ner_v1"
OUT_STATS = OUT_DIR / "stats"
OUT_LOGS = OUT_DIR / "logs"
OUT_MODELS = OUT_DIR / "models"
OUT_LEXICON_SNAPSHOT = OUT_DIR / "lexicons"

MENTIONS_PATH = OUT_DIR / "entities_mentions.jsonl"
CANONICAL_PATH = OUT_DIR / "entities_canonical.jsonl"
INDEX_PATH = OUT_DIR / "doc_entities_index.csv"
SUMMARY_PATH = OUT_STATS / "entities_summary.csv"
DOCBIN_PATH = OUT_MODELS / "spacy_docbin.spacy"
ERROR_LOG = OUT_LOGS / "errors_ner.log"

LEXICON_DIR = ROOT / "processing" / "ner" / "lexicons"

#
# /**
#  * @function ensure_dirs
#  * @description 确保输出目录存在
#  */

def ensure_dirs() -> None:
    for p in [OUT_DIR, OUT_STATS, OUT_LOGS, OUT_MODELS, OUT_LEXICON_SNAPSHOT]:
        p.mkdir(parents=True, exist_ok=True)


# --------------------------- 工具函数 ---------------------------
# /**
#  * @function jdump
#  * @description JSON 序列化（优先 orjson）
#  * @param {dict} obj
#  * @returns {str}
#  */

def jdump(obj: Dict[str, Any]) -> str:
    if orjson:
        return orjson.dumps(obj, option=orjson.OPT_APPEND_NEWLINE | orjson.OPT_SERIALIZE_NUMPY).decode("utf-8")
    return json.dumps(obj, ensure_ascii=False) + "\n"


# /**
#  * @function read_text_file
#  * @param {Path} path
#  * @returns {str}
#  */

def read_text_file(path: Path) -> str:
    return path.read_text(encoding="utf-8", errors="ignore")


# --------------------------- 文件头解析 ---------------------------
# /**
#  * @function parse_header
#  * @description 解析前 5 行元信息；返回 (meta, body, header_len)
#  * @param {str} text
#  * @returns {[dict, str, int]}
#  */

def parse_header(text: str) -> Tuple[Dict[str, str], str, int]:
    lines = text.splitlines()
    meta: Dict[str, str] = {"title": "", "billId": "", "latestActionDate": "", "url": "", "source": ""}
    header_len = 0
    for i in range(min(5, len(lines))):
        line = lines[i].strip()
        if line.startswith("### "):
            header_len = i + 1
            key_val = line[4:]
            if ":" in key_val:
                k, v = key_val.split(":", 1)
                meta[k.strip()] = v.strip()
    body = "\n".join(lines[header_len:]) if header_len > 0 else text
    # bill_key: 119|hr|4830 from billId "hr.4830 (119th)"
    bill_key = ""
    m = re.search(r"([hs]\.?\s?(?:r|res|j\.?res|con\.?res)?)[\s\.]*(\d{1,5}).*?\((\d{2,3})th\)", meta.get("billId", ""), flags=re.I)
    if m:
        bill_type = m.group(1).lower().replace(".", "").replace(" ", "")
        bill_type = bill_type.replace("res", "res").replace("conres", "conres").replace("jres", "jres")
        number = m.group(2)
        congress = m.group(3)
        bill_key = f"{congress}|{bill_type}|{number}"
    meta["bill_key"] = bill_key
    return meta, body, header_len


# --------------------------- 词典加载与规则 ---------------------------
# /**
#  * @function load_lexicons
#  * @description 从 CSV 读取词典，构造别名索引
#  * @returns {dict}
#  */

def load_lexicons() -> Dict[str, Dict[str, Dict[str, Any]]]:
    """
    返回结构：
    {
      "companies": {"huawei": {"canonical_id": "company:huawei", "name": "Huawei", "type": "CUSTOM_COMPANY", "aliases": [..], "country": "CN"}, ...},
      "agencies": {...},
      ...
    }
    同时将 LEXICON_DIR 快照复制到 OUT_LEXICON_SNAPSHOT
    """
    lex = {"companies": {}, "agencies": {}, "committees": {}, "policies": {}, "places_cn": {}}

    def _read_csv(p: Path) -> List[Dict[str, str]]:
        rows: List[Dict[str, str]] = []
        if pd is not None:
            try:
                df = pd.read_csv(p)
                rows = df.fillna("").to_dict(orient="records")
                return rows
            except Exception:
                pass
        with p.open("r", encoding="utf-8") as f:
            r = csv.DictReader(f)
            for row in r:
                rows.append({k: (v or "") for k, v in row.items()})
        return rows

    for name in lex.keys():
        csv_path = LEXICON_DIR / f"{name}.csv"
        if not csv_path.exists():
            continue
        for row in _read_csv(csv_path):
            name_val = (row.get("name") or "").strip()
            if not name_val:
                continue
            typ = (row.get("type") or "").strip() or f"CUSTOM_{name.upper()}"
            canonical_id = (row.get("canonical_id") or f"{name.rstrip('s')}:{re.sub(r'[^a-z0-9]+','-', name_val.lower()).strip('-')}")
            aliases = [a.strip() for a in (row.get("aliases") or "").split("|") if a.strip()]
            country = (row.get("country") or "").strip()
            norm_keys = set([name_val.lower()]) | {a.lower() for a in aliases}
            entry = {"canonical_id": canonical_id, "name": name_val, "aliases": aliases, "type": typ, "country": country}
            for k in norm_keys:
                lex[name][k] = entry
    # 快照
    if OUT_LEXICON_SNAPSHOT.exists():
        shutil.rmtree(OUT_LEXICON_SNAPSHOT)
    shutil.copytree(LEXICON_DIR, OUT_LEXICON_SNAPSHOT)
    return lex


# --------------------------- Regex 匹配（法案/Public Law） ---------------------------
BILL_RE = re.compile(r"(?:(?:H|S)\.? ?(?:R|Res|J\.?Res|Con\.?Res)\.? ?\d{1,5})|(?:(?:H|S)\.? ?\d{1,5})", re.I)
PUBLIC_LAW_RE = re.compile(r"Public Law (?:No\. )?\d{3}[-–]\d{1,4}", re.I)


# --------------------------- NLP 管线 ---------------------------
# /**
#  * @function load_nlp
#  * @description 加载 spaCy 管线（优先 trf，回退 sm），添加 EntityRuler
#  * @returns {Language}
#  */

def load_nlp(lex: Dict[str, Dict[str, Dict[str, Any]]]) -> Language:
    spacy.prefer_gpu()
    nlp: Optional[Language] = None
    model_candidates = ["en_core_web_trf", "en_core_web_sm"]
    for m in model_candidates:
        try:
            nlp = spacy.load(m)
            break
        except Exception:
            continue
    if nlp is None:
        raise RuntimeError("无法加载 spaCy 英文模型，请先下载 en_core_web_sm 或 en_core_web_trf")

    # 句子切分（部分小模型需启用 senter）
    if "senter" not in nlp.pipe_names and "sentencizer" not in nlp.pipe_names:
        nlp.add_pipe("sentencizer")

    # EntityRuler：从词典生成规则
    ruler = nlp.add_pipe("entity_ruler", config={"overwrite_ents": False})  # 不覆盖模型结果
    patterns: List[Dict[str, Any]] = []

    def add_lexicon_patterns(bucket: str, label: str):
        for key, entry in lex[bucket].items():
            # 用 phrase 模式（大小写不敏感）
            patterns.append({"label": label, "pattern": key, "id": entry["canonical_id"], "attrs": {"_source": "ruler"}})

    add_lexicon_patterns("companies", "CUSTOM_COMPANY")
    add_lexicon_patterns("agencies", "CUSTOM_AGENCY")
    add_lexicon_patterns("committees", "CUSTOM_COMMITTEE")
    add_lexicon_patterns("policies", "CUSTOM_POLICY")
    add_lexicon_patterns("places_cn", "CUSTOM_PLACE_CN")

    ruler.add_patterns(patterns)
    return nlp


# --------------------------- 标签与标准化 ---------------------------
# /**
#  * @function normalize_text
#  * @description 文本归一（去首尾标点与多空格）
#  */

def normalize_text(s: str) -> str:
    s2 = s.strip().strip("\'\"()[]{}.,:;·“”‘’")
    s2 = re.sub(r"\s+", " ", s2)
    return s2


# /**
#  * @function label_and_canonical
#  * @description 综合模型/规则结果，映射到自定义标签与 canonical_id
#  * @returns {[label, canonical_id, source, extra_meta]}
#  */

def label_and_canonical(span_text: str, base_label: str, lex: Dict[str, Dict[str, Dict[str, Any]]]) -> Tuple[str, str, str, Dict[str, Any]]:
    t = normalize_text(span_text)
    low = t.lower()

    # Regex 优先：法案 / Public Law
    if BILL_RE.search(t) or PUBLIC_LAW_RE.search(t):
        return "CUSTOM_BILL", f"bill:{re.sub(r'[^a-z0-9]+','-', low).strip('-')}", "regex", {"orig_label": base_label}

    # 词典优先：company/agency/committee/policy/place
    for bucket, label in [
        ("companies", "CUSTOM_COMPANY"),
        ("agencies", "CUSTOM_AGENCY"),
        ("committees", "CUSTOM_COMMITTEE"),
        ("policies", "CUSTOM_POLICY"),
        ("places_cn", "CUSTOM_PLACE_CN"),
    ]:
        if low in lex[bucket]:
            e = lex[bucket][low]
            return label, e["canonical_id"], "ruler", {"orig_label": base_label, "country": e.get("country")}

    # 地理细化：常见中国相关词
    place_map = {
        "china": "place:china",
        "prc": "place:china",
        "people's republic of china": "place:china",
        "mainland china": "place:china",
        "taiwan": "place:taiwan",
        "hong kong": "place:hong_kong",
        "xinjiang": "place:xinjiang",
        "tibet": "place:tibet",
    }
    if low in place_map:
        return "CUSTOM_PLACE_CN", place_map[low], "rule", {"orig_label": base_label}

    # 保留通用标签
    return base_label, f"{base_label.lower()}:{re.sub(r'[^a-z0-9]+','-', low).strip('-')}", "model", {"orig_label": base_label}


# --------------------------- 主处理逻辑 ---------------------------
# /**
#  * @function iter_docs
#  * @description 迭代输入目录中的 .txt 文档，解析 meta
#  */

def iter_docs() -> Iterable[Tuple[str, Dict[str, str], str]]:
    txts = sorted(TXT_DIR.glob("*.txt"))
    for p in txts:
        txt = read_text_file(p)
        meta, body, _ = parse_header(txt)
        yield p.name, meta, body


# /**
#  * @function save_stats
#  * @description 保存统计（各类计数、Top-N）到 CSV
#  */

def save_stats(doc_entity_counts: Dict[Tuple[str, str], int], canonical_meta: Dict[str, Dict[str, Any]]) -> None:
    # label 计数
    label_counts: Dict[str, int] = {}
    entity_counts: Dict[str, int] = {}
    for (doc_id, cid), c in doc_entity_counts.items():
        label = canonical_meta.get(cid, {}).get("type", "UNKNOWN")
        label_counts[label] = label_counts.get(label, 0) + c
        entity_counts[cid] = entity_counts.get(cid, 0) + c

    # 写 summary
    with SUMMARY_PATH.open("w", encoding="utf-8", newline="") as f:
        w = csv.writer(f)
        w.writerow(["label", "total_mentions"])
        for lab, cnt in sorted(label_counts.items(), key=lambda x: x[0]):
            w.writerow([lab, cnt])
        w.writerow([])
        w.writerow(["top_entity", "label", "count", "name"])
        top_list = sorted(entity_counts.items(), key=lambda x: x[1], reverse=True)[:50]
        for cid, cnt in top_list:
            meta = canonical_meta.get(cid, {})
            w.writerow([cid, meta.get("type"), cnt, meta.get("name")])


# /**
#  * @function main
#  * @description 入口：跑 NER、合并、标准化、写出产物
#  */

def main() -> None:
    ensure_dirs()
    if not TXT_DIR.exists():
        print(f"输入目录不存在: {TXT_DIR}")
        sys.exit(1)

    # 加载词典与 NLP
    lex = load_lexicons()
    nlp = load_nlp(lex)

    # 输出文件
    mentions_f = MENTIONS_PATH.open("w", encoding="utf-8")

    # 结果累积
    canonical_meta: Dict[str, Dict[str, Any]] = {}
    doc_entity_counts: Dict[Tuple[str, str], int] = {}

    # 处理每个文档
    total_docs = 0
    errors: List[str] = []

    for fname, meta, body in tqdm(list(iter_docs()), desc="NER"):
        total_docs += 1
        try:
            doc: Doc = nlp(body)
            # 将 Regex 命中加入 ents（句级循环方便拿句子文本）
            for sent in doc.sents:
                sent_text = sent.text
                regex_spans: List[Span] = []
                for m in BILL_RE.finditer(sent_text):
                    start = sent.start_char + m.start()
                    end = sent.start_char + m.end()
                    regex_spans.append(doc.char_span(start, end, label="CUSTOM_BILL"))
                for m in PUBLIC_LAW_RE.finditer(sent_text):
                    start = sent.start_char + m.start()
                    end = sent.start_char + m.end()
                    regex_spans.append(doc.char_span(start, end, label="CUSTOM_BILL"))
                regex_spans = [sp for sp in regex_spans if sp is not None]
                # 合并：保留较长的 span
                doc.ents = tuple(filter_spans(list(doc.ents) + regex_spans))

            # 输出逐提及
            for ent in doc.ents:
                label, cid, source, extra = label_and_canonical(ent.text, ent.label_, lex)
                # 若规则/词典与模型冲突，保留领域标签，并记录原标签
                orig = extra.get("orig_label", ent.label_)
                if source in {"ruler", "regex", "rule"} and label.startswith("CUSTOM_") and orig != label:
                    extra["orig_label"] = orig
                # 置信度：简化处理
                conf = 1.0 if source in {"ruler", "regex", "rule"} else 0.90

                mention = {
                    "doc_id": fname,
                    "bill_key": meta.get("bill_key"),
                    "title": meta.get("title"),
                    "mention": ent.text,
                    "label": label,
                    "start": ent.start_char,
                    "end": ent.end_char,
                    "sentence": ent.sent.text,
                    "confidence": round(conf, 2),
                    "source": "model" if source == "model" else ("ruler" if source == "ruler" else "regex"),
                    "meta": {"orig_label": orig, "url": meta.get("url")},
                }
                mentions_f.write(jdump(mention))

                # 累计 doc-entity 计数
                key = (fname, cid)
                doc_entity_counts[key] = doc_entity_counts.get(key, 0) + 1

                # 记录 canonical 元信息（只记录出现过的）
                if cid not in canonical_meta:
                    canonical_meta[cid] = {
                        "canonical_id": cid,
                        "name": normalize_text(ent.text),
                        "aliases": [],
                        "type": label,
                        "country": extra.get("country", ""),
                    }
        except Exception as e:
            errors.append(f"{fname}: {e}")
            continue

    mentions_f.close()

    # 写 doc_entities_index.csv
    with INDEX_PATH.open("w", encoding="utf-8", newline="") as f:
        w = csv.writer(f)
        w.writerow(["doc_id", "bill_key", "canonical_id", "label", "count"])
        # 为了写 bill_key，需一个文件名→bill_key 的快速映射
        bill_keys: Dict[str, str] = {}
        # 轻量重扫 header（避免保留全部 meta）
        for p in sorted(TXT_DIR.glob("*.txt")):
            meta, _, _ = parse_header(read_text_file(p))
            bill_keys[p.name] = meta.get("bill_key", "")
        for (doc_id, cid), cnt in sorted(doc_entity_counts.items()):
            lab = canonical_meta.get(cid, {}).get("type", "")
            w.writerow([doc_id, bill_keys.get(doc_id, ""), cid, lab, cnt])

    # 写 canonical.jsonl
    with CANONICAL_PATH.open("w", encoding="utf-8") as f:
        for cid, meta in canonical_meta.items():
            f.write(jdump(meta))

    # 保存统计
    save_stats(doc_entity_counts, canonical_meta)

    # 错误日志
    if errors:
        with ERROR_LOG.open("w", encoding="utf-8") as f:
            for line in errors:
                f.write(line + "\n")

    # 友好提示
    print(f"已处理文档: {total_docs}; Mentions: {sum(doc_entity_counts.values())}; Canonical 实体: {len(canonical_meta)}")
    print(f"产物目录: {OUT_DIR}")


if __name__ == "__main__":
    main()

