# Events V1（立法动作 + 政策措施）

## 运行
```bash
# 环境变量：CONGRESS_API_KEY（必须用于 actions）
python processing/events/extract_events_v1.py \
  --outdir artifacts/events_v1 \
  --txtdir out/2025_txt \
  --manifest out/2025_txt/manifest.csv \
  --mentions artifacts/ner_v1/entities_mentions.jsonl \
  --canon artifacts/ner_v1/entities_canonical.jsonl
```

## 输出
- artifacts/events_v1/bill_events.csv
- artifacts/events_v1/policy_measures.csv
- artifacts/events_v1/run.log

## 规则摘要
- BillEvent：基于 actions 文本映射到类型（INTRODUCED/REFERRED/...）
- PolicyMeasure：句级触发词（如 export control/UFLPA/CFIUS/ban/prohibit 等）+ 同句实体作为 target_ids

## 限速与重试
- ≤3 req/s；429/5xx 指数退避，最多 5 次

## 后续
- build_graph_files_v1.py 生成图谱 nodes/edges CSV（待实现）

