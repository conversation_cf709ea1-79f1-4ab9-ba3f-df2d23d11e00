# -*- coding: utf-8 -*-
"""
NER V1 统计汇总脚本（带过滤与规范化聚合）

用法示例：
python processing/ner/summarize_entities_v1_1.py \
  --mentions artifacts/ner_v1/entities_mentions.jsonl \
  --canon artifacts/ner_v1/entities_canonical.jsonl \
  --outdir artifacts/ner_v1/stats \
  --topk 50
"""
from __future__ import annotations
import argparse, json, csv, os, re, sys
from pathlib import Path
from typing import Dict, Tuple, Any, Iterable, Set, List, Optional
from datetime import datetime

try:
    import orjson
except Exception:
    orjson = None  # type: ignore

# --------------------------- 配置 ---------------------------
EXCLUDE_LABELS = {"CARDINAL","ORDINAL","DATE","TIME","PERCENT","QUANTITY","MONEY"}
ALLOW_BASE = {"PERSON","ORG","GPE","NORP","LAW"}
ALLOW_PREFIX = "CUSTOM_"

# --------------------------- 工具函数 ---------------------------
# /**
#  * @function loads_json
#  * @description 解析 JSON 字符串，优先 orjson
#  * @param {str} line
#  * @returns {dict}
#  */

def loads_json(line: str) -> Dict[str, Any]:
    if orjson:
        return orjson.loads(line)
    return json.loads(line)


# /**
#  * @function normalize_text
#  * @description 规范化 mention 文本：去首尾标点、压缩空格并转小写
#  * @param {str} s
#  * @returns {str}
#  */

def normalize_text(s: str) -> str:
    s2 = s.strip().strip("\'\"()[]{}.,:;·“”‘’")
    s2 = re.sub(r"\s+", " ", s2)
    return s2.lower()


# /**
#  * @function build_fallback_canon
#  * @description 构建回退 canonical_id：{label_lower}:{slug}
#  * @param {str} label
#  * @param {str} text
#  */

def build_fallback_canon(label: str, text: str) -> str:
    low = normalize_text(text)
    slug = re.sub(r"[^a-z0-9]+", "-", low).strip("-")
    return f"{label.lower()}:{slug}"


# /**
#  * @function contains_digits
#  * @description 文本是否包含数字（用于过滤）
#  */

def contains_digits(s: str) -> bool:
    return any(ch.isdigit() for ch in s)


# /**
#  * @function keep_record
#  * @description 过滤逻辑：白名单标签或 CUSTOM_*，并通过文本级过滤
#  */

def keep_record(label: str, mention: str) -> bool:
    # CUSTOM_BILL 例外：保留法案编号（即使包含数字）
    if label == "CUSTOM_BILL":
        return True
    # 其他标签：过滤掉包含数字的 mention
    if contains_digits(mention):
        return False
    if label.startswith(ALLOW_PREFIX):
        return True
    if label in ALLOW_BASE:
        # 需要至少含一个字母
        return any(c.isalpha() for c in mention)
    # 黑名单等
    return False


# --------------------------- 主逻辑 ---------------------------
# /**
#  * @function load_canonical_map
#  * @description 从 entities_canonical.jsonl 构建 mention->canonical 映射（基于 name 与 aliases）
#  * @returns {dict}
#  */

def load_canonical_map(canon_path: Optional[Path]) -> Tuple[Dict[str, str], Dict[str, Dict[str, Any]]]:
    alias2cid: Dict[str, str] = {}
    meta: Dict[str, Dict[str, Any]] = {}
    if not canon_path or not canon_path.exists():
        return alias2cid, meta
    with canon_path.open("r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            try:
                rec = loads_json(line)
            except Exception:
                continue
            cid = rec.get("canonical_id")
            name = (rec.get("name") or "").strip()
            aliases = rec.get("aliases") or []
            typ = rec.get("type")
            if not cid or not name:
                continue
            meta[cid] = {"canonical_id": cid, "name": name, "aliases": aliases, "type": typ}
            alias2cid[normalize_text(name)] = cid
            for a in aliases:
                if a:
                    alias2cid[normalize_text(a)] = cid
    return alias2cid, meta


# /**
#  * @function aggregate
#  * @description 聚合 mentions 为 (mention_count, doc_count)
#  */

def aggregate(mentions_iter: Iterable[Dict[str, Any]], alias2cid: Dict[str, str], canon_meta: Dict[str, Dict[str, Any]]):
    # (cid,label) -> mention_count
    all_counts: Dict[Tuple[str, str], int] = {}
    all_docs: Dict[Tuple[str, str], Set[str]] = {}

    filt_counts: Dict[Tuple[str, str], int] = {}
    filt_docs: Dict[Tuple[str, str], Set[str]] = {}

    date_counts: Dict[Tuple[str, str], int] = {}
    date_docs: Dict[Tuple[str, str], Set[str]] = {}

    total = 0
    total_kept = 0

    for rec in mentions_iter:
        total += 1
        doc_id = rec.get("doc_id") or ""
        label = rec.get("label") or ""
        mention = rec.get("mention") or ""
        if not label or not mention:
            continue
        norm = normalize_text(mention)
        cid = alias2cid.get(norm) or build_fallback_canon(label, norm)
        key = (cid, label)

        # 全量
        all_counts[key] = all_counts.get(key, 0) + 1
        all_docs.setdefault(key, set()).add(doc_id)

        # DATE/TIME 归档（全量）
        if label in {"DATE", "TIME"}:
            date_counts[key] = date_counts.get(key, 0) + 1
            date_docs.setdefault(key, set()).add(doc_id)

        # 过滤
        if keep_record(label, mention):
            filt_counts[key] = filt_counts.get(key, 0) + 1
            filt_docs.setdefault(key, set()).add(doc_id)
            total_kept += 1

        # 维护 canon_meta 缺失 name 的情况（fallback 用原 mention）
        if cid not in canon_meta:
            canon_meta[cid] = {"canonical_id": cid, "name": mention.strip(), "aliases": [], "type": label}

    return {
        "all_counts": all_counts,
        "all_docs": all_docs,
        "filt_counts": filt_counts,
        "filt_docs": filt_docs,
        "date_counts": date_counts,
        "date_docs": date_docs,
        "total": total,
        "total_kept": total_kept,
    }


# /**
#  * @function write_summary_csv
#  * @description 写出汇总 CSV：canonical_id,label,name,mention_count,doc_count
#  */

def write_summary_csv(path: Path, counts: Dict[Tuple[str, str], int], docs: Dict[Tuple[str, str], Set[str]], canon_meta: Dict[str, Dict[str, Any]]):
    with path.open("w", encoding="utf-8", newline="") as f:
        w = csv.writer(f)
        w.writerow(["canonical_id", "label", "name", "mention_count", "doc_count"])
        for (cid, label), mcnt in sorted(counts.items(), key=lambda x: (-x[1], x[0][0])):
            dcnt = len(docs.get((cid, label), set()))
            name = canon_meta.get(cid, {}).get("name") or cid
            w.writerow([cid, label, name, mcnt, dcnt])


# /**
#  * @function write_top_overall
#  * @description 写出 overall Top-N（基于过滤表）
#  */

def write_top_overall(path: Path, counts: Dict[Tuple[str, str], int], docs: Dict[Tuple[str, str], Set[str]], canon_meta: Dict[str, Dict[str, Any]], topk: int):
    items = [((cid, label), cnt) for (cid, label), cnt in counts.items()]
    items.sort(key=lambda x: (-x[1], x[0][0][0]))
    with path.open("w", encoding="utf-8", newline="") as f:
        w = csv.writer(f)
        w.writerow(["rank", "canonical_id", "label", "name", "mention_count", "doc_count"])
        for i, ((cid, label), cnt) in enumerate(items[:topk], start=1):
            name = canon_meta.get(cid, {}).get("name") or cid
            dcnt = len(docs.get((cid, label), set()))
            w.writerow([i, cid, label, name, cnt, dcnt])


# /**
#  * @function write_top_by_label
#  * @description 按标签写出 Top-N
#  */

def write_top_by_label(outdir: Path, counts: Dict[Tuple[str, str], int], docs: Dict[Tuple[str, str], Set[str]], canon_meta: Dict[str, Dict[str, Any]], topk: int):
    label_set: Set[str] = set([lab for (_, lab) in counts.keys()])
    targets = set(ALLOW_BASE)
    targets |= set([lab for lab in label_set if lab.startswith("CUSTOM_")])
    for lab in sorted(targets):
        items = [((cid, l), cnt) for (cid, l), cnt in counts.items() if l == lab]
        items.sort(key=lambda x: (-x[1], x[0][0][0]))
        path = outdir / f"top_{lab}.csv"
        with path.open("w", encoding="utf-8", newline="") as f:
            w = csv.writer(f)
            w.writerow(["label_specific_rank", "canonical_id", "label", "name", "mention_count", "doc_count"])
            for i, ((cid, l), cnt) in enumerate(items[:topk], start=1):
                name = canon_meta.get(cid, {}).get("name") or cid
                dcnt = len(docs.get((cid, l), set()))
                w.writerow([i, cid, l, name, cnt, dcnt])


# /**
#  * @function iter_mentions
#  * @description 流式读取 mentions.jsonl
#  */

def iter_mentions(path: Path) -> Iterable[Dict[str, Any]]:
    with path.open("r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            try:
                yield loads_json(line)
            except Exception:
                continue


# /**
#  * @function main
#  */

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--mentions", type=str, default="artifacts/ner_v1/entities_mentions.jsonl")
    ap.add_argument("--canon", type=str, default="artifacts/ner_v1/entities_canonical.jsonl")
    ap.add_argument("--outdir", type=str, default="artifacts/ner_v1/stats")
    ap.add_argument("--topk", type=int, default=50)
    args = ap.parse_args()

    mentions_path = Path(args.mentions)
    canon_path = Path(args.canon)
    outdir = Path(args.outdir)
    outdir.mkdir(parents=True, exist_ok=True)

    # 日志
    run_log = outdir / "stats_run.log"
    with run_log.open("w", encoding="utf-8") as lg:
        lg.write(f"[START] {datetime.utcnow().isoformat()}Z\n")
        lg.write(f"mentions: {mentions_path}\ncanon: {canon_path}\noutdir: {outdir}\n")

    if not mentions_path.exists():
        print(f"找不到 mentions 文件: {mentions_path}")
        sys.exit(1)

    alias2cid, canon_meta = load_canonical_map(canon_path if canon_path.exists() else None)

    stats = aggregate(iter_mentions(mentions_path), alias2cid, canon_meta)

    # 写全量与过滤表
    all_csv = outdir / "entities_summary_all.csv"
    filt_csv = outdir / "entities_summary_filtered.csv"
    write_summary_csv(all_csv, stats["all_counts"], stats["all_docs"], canon_meta)

    # 从过滤表剔除黑名单（虽然 keep_record 已排除，这里二次保护）
    filt_counts = {k: v for k, v in stats["filt_counts"].items() if k[1] not in EXCLUDE_LABELS}
    filt_docs = {k: v for k, v in stats["filt_docs"].items() if k[1] not in EXCLUDE_LABELS}
    write_summary_csv(filt_csv, filt_counts, filt_docs, canon_meta)

    # 写 overall Top-N（过滤后）
    write_top_overall(outdir / "top_entities_overall_filtered.csv", filt_counts, filt_docs, canon_meta, args.topk)

    # 按标签 Top-N（过滤后）
    write_top_by_label(outdir, filt_counts, filt_docs, canon_meta, args.topk)

    # DATE/TIME 汇总
    write_summary_csv(outdir / "date_terms_all.csv", stats["date_counts"], stats["date_docs"], canon_meta)

    # 日志尾
    kept_ratio = (stats["total_kept"] / stats["total"]) if stats["total"] else 0.0
    with run_log.open("a", encoding="utf-8") as lg:
        lg.write(f"total_mentions={stats['total']} kept={stats['total_kept']} kept_ratio={kept_ratio:.2%}\n")
        lg.write(f"[END] {datetime.utcnow().isoformat()}Z\n")

    # 控制台摘要
    print(f"总 mentions: {stats['total']}  过滤后: {stats['total_kept']}  通过率: {kept_ratio:.2%}")
    print(f"输出: {all_csv}, {filt_csv}, top_entities_overall_filtered.csv, top_*.csv, date_terms_all.csv")


if __name__ == "__main__":
    main()

